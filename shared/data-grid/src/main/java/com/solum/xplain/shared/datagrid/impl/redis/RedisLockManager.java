package com.solum.xplain.shared.datagrid.impl.redis;

import jakarta.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.stereotype.Component;

/**
 * Manages Redis distributed locks with automatic heartbeat functionality.
 *
 * <p>The RedisLockManager handles the complete lifecycle of locks including acquisition, automatic
 * renewal through heartbeat mechanism, and proper cleanup during application shutdown.
 *
 * <ul>
 *   <li>Automatic lock acquisition with heartbeat monitoring
 *   <li>Periodic lock renewal to prevent expiration
 *   <li>Graceful lock release and cleanup
 *   <li>Thread-safe operations using concurrent data structures
 *   <li>Proper resource cleanup during application shutdown
 * </ul>
 *
 * <p>The heartbeat mechanism runs at 70% of the default TTL (7 seconds for a 10-second TTL) to
 * ensure locks are renewed before they expire.
 */
@Getter
@Slf4j
@Component
@NullMarked
public class RedisLockManager {

  // ~70% of default TTL (10s)
  public static final long RENEWAL_INTERVAL_MS = 7000;

  /**
   * Scheduled executor service for managing heartbeat tasks. Uses 2 daemon threads with custom
   * naming for easier debugging.
   */
  private final ScheduledExecutorService heartbeatScheduler;

  /**
   * Map of lock IDs to their active heartbeat tasks. Thread-safe concurrent map to
   * handle multiple lock operations.
   */
  private final ConcurrentHashMap<String, HeartbeatTask> heartbeatTasksByLockId;

  /**
   * Internal class representing a heartbeat task for a specific Redis lock.
   *
   * <p>Each HeartbeatTask is responsible for:
   *
   * <ul>
   *   <li>Periodically renewing its associated lock
   *   <li>Monitoring lock validity and expiration
   *   <li>Self-termination when lock becomes invalid
   * </ul>
   */
  private static class HeartbeatTask {
    private final RedisClusterLock lock;

    private final String lockKey;

    /** The scheduled future representing the periodic heartbeat execution */
    private volatile @Nullable ScheduledFuture<?> futureHeartBeat;

    private volatile boolean hasHeartBeatStopped = false;

    /**
     * Creates a new heartbeat task for the specified lock.
     *
     * @param lock the Redis cluster lock to manage
     * @param lockKey the unique key identifying the lock
     */
    HeartbeatTask(RedisClusterLock lock, String lockKey) {
      this.lock = lock;
      this.lockKey = lockKey;
    }

    /**
     * Associates a scheduled future with this heartbeat task.
     *
     * @param futureHeartBeat the scheduled future representing the periodic execution
     */
    void setFutureHeartBeat(ScheduledFuture<?> futureHeartBeat) {
      this.futureHeartBeat = futureHeartBeat;
    }

    /**
     * Performs a single heartbeat renewal attempt.
     *
     * <p>This method:
     *
     * <ul>
     *   <li>Checks if the task has been stopped
     *   <li>Validates the current lock state
     *   <li>Attempts to renew the lock
     *   <li>Stops the heartbeat if renewal fails
     * </ul>
     *
     * <p>If any error occurs during renewal, the heartbeat is stopped and appropriate logging is
     * performed.
     */
    void renew() {
      if (hasHeartBeatStopped) return;

      try {
        if (!lock.isCurrentLockValid() || lock.isLockExpired()) {
          log.warn("Lock no longer valid or expired, stopping heartbeat: {}", lockKey);
          stop();
          return;
        }

        boolean renewed = lock.renewLock();
        if (!renewed) {
          log.warn("Failed to renew lock, stopping heartbeat: {}", lockKey);
          stop();
        }
      } catch (Exception e) {
        log.error("Error renewing Redis lock {}: {}", lockKey, e.getMessage(), e);
        stop();
      }
    }

    /** Stops this heartbeat task. */
    void stop() {
      hasHeartBeatStopped = true;
      ScheduledFuture<?> currentFuture = this.futureHeartBeat;
      if (currentFuture != null && !currentFuture.isCancelled()) {
        currentFuture.cancel(false);
      }
    }
  }

  /**
   * Constructs a new RedisLockManager.
   *
   * <p>Initializes the heartbeat scheduler with 2 daemon threads and creates the concurrent map for
   * tracking active locks.
   */
  public RedisLockManager() {
    this.heartbeatScheduler =
        Executors.newScheduledThreadPool(
            2,
            r -> {
              Thread t = new Thread(r, "lock-heartbeat");
              t.setDaemon(true);
              return t;
            });

    this.heartbeatTasksByLockId = new ConcurrentHashMap<>();
  }

  /**
   * Attempts to acquire a Redis lock and starts automatic heartbeat if successful. Tries to acquire
   * the lock immediately (non-blocking). If successful, it automatically starts a heartbeat to keep
   * the lock alive.
   */
  public boolean acquireWithHeartbeat(RedisClusterLock lock) {
    if (lock.tryLock()) {
      startHeartbeat(lock);
      log.info("Acquired Redis lock: {}", lock.getName());
      return true;
    }
    return false;
  }

  /**
   * Attempts to acquire a Redis lock with a timeout and starts automatic heartbeat if successful.
   *
   * <p>This method will wait for the specified timeout period to acquire the lock. If successful
   * within the timeout, it automatically starts a heartbeat to keep the lock alive.
   *
   * @param lock the Redis cluster lock to acquire
   * @param timeout the maximum time to wait for the lock
   * @param unit the time unit of the timeout argument
   * @return {@code true} if the lock was successfully acquired within the timeout and heartbeat
   *     started, {@code false} otherwise
   */
  public boolean acquireWithHeartbeat(RedisClusterLock lock, long timeout, TimeUnit unit) {
    if (lock.tryLock(timeout, unit)) {
      startHeartbeat(lock);
      log.debug("Acquired Redis lock with timeout: {}", lock.getName());
      return true;
    }
    return false;
  }

  /**
   * Releases a Redis lock and stops its associated heartbeat.
   *
   * <ul>
   *   <li>Stops the heartbeat task for the lock
   *   <li>Removes the lock from active locks tracking
   *   <li>Releases the actual Redis lock
   * </ul>
   *
   * <p>Any errors during lock release are logged but do not throw exceptions to ensure cleanup
   * operations can continue.
   *
   * @param lock the Redis cluster lock to release
   */
  public void release(RedisClusterLock lock) {
    String lockKey = lock.getName();

    // Stop heartbeat
    HeartbeatTask task = heartbeatTasksByLockId.remove(lockKey);
    if (task != null) {
      task.stop();
    }

    // Release the lock
    try {
      lock.unlock();
      log.info("Released Redis lock: {}", lockKey);
    } catch (Exception e) {
      log.warn("Error releasing Redis lock {}: {}", lockKey, e.getMessage(), e);
    }
  }

  /**
   * Starts a heartbeat for a Redis lock.
   *
   * <p>The heartbeat runs at fixed intervals (7 seconds by default, which is 70% of the default
   * 10-second TTL) to ensure the lock is renewed before expiration.
   *
   * <p>If a heartbeat is already running for this lock, the previous one will be replaced by the
   * new heartbeat task.
   *
   * @param lock the Redis cluster lock to start heartbeat for
   */
  public void startHeartbeat(RedisClusterLock lock) {
    String lockKey = lock.getName();

    HeartbeatTask task = new HeartbeatTask(lock, lockKey);
    ScheduledFuture<?> future =
        heartbeatScheduler.scheduleAtFixedRate(
            task::renew, RENEWAL_INTERVAL_MS, RENEWAL_INTERVAL_MS, TimeUnit.MILLISECONDS);

    task.setFutureHeartBeat(future);
    heartbeatTasksByLockId.put(lockKey, task);
    log.info("Started heartbeat for Redis lock: {}", lockKey);
  }

  /**
   * Shuts down the RedisLockManager and performs cleanup of all resources.
   *
   * <p>This method is automatically called during Spring application shutdown due to the
   * {@code @PreDestroy} annotation. It performs the following cleanup:
   *
   * <ul>
   *   <li>Stops all active heartbeat tasks
   *   <li>Releases all active Redis locks
   *   <li>Clears the active locks tracking map
   *   <li>Shuts down the heartbeat scheduler
   *   <li>Waits for graceful termination with a 2-second timeout
   * </ul>
   *
   * <p>Any errors during individual lock releases are logged but do not prevent the overall
   * shutdown process from completing.
   */
  @PreDestroy
  public void shutdown() {
    log.info("Shutting down RedisLockManager with {} active locks", heartbeatTasksByLockId.size());

    // Stop all heartbeats and release associated locks
    for (String lockKey : heartbeatTasksByLockId.keySet()) {
      HeartbeatTask task = heartbeatTasksByLockId.remove(lockKey);
      if (task != null) {
        task.stop();
        try {
          task.lock.unlock();
          log.info("Released lock during shutdown: {}", lockKey);
        } catch (Exception e) {
          log.warn("Failed to release lock {} during shutdown: {}", lockKey, e.getMessage(), e);
        }
      }
    }

    heartbeatTasksByLockId.clear();
    heartbeatScheduler.shutdown();

    try {
      if (!heartbeatScheduler.awaitTermination(2, TimeUnit.SECONDS)) {
        heartbeatScheduler.shutdownNow();
      }
    } catch (InterruptedException e) {
      heartbeatScheduler.shutdownNow();
      Thread.currentThread().interrupt();
    }

    log.info("RedisLockManager shutdown complete");
  }
}

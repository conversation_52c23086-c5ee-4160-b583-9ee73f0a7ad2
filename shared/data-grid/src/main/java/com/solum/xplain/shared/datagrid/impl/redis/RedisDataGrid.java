package com.solum.xplain.shared.datagrid.impl.redis;

import com.solum.xplain.shared.datagrid.AtomicCounter;
import com.solum.xplain.shared.datagrid.ClusterLock;
import com.solum.xplain.shared.datagrid.DataGrid;
import com.solum.xplain.shared.datagrid.FencingTokenService;
import com.solum.xplain.shared.datagrid.KeyValueCache;
import com.solum.xplain.shared.datagrid.LockManager;
import com.solum.xplain.shared.datagrid.ManagedClusterLock;
import com.solum.xplain.shared.datagrid.PubSubTopic;
import com.solum.xplain.shared.datagrid.ValueSet;
import jakarta.annotation.PostConstruct;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
@NullMarked
public class RedisDataGrid implements DataGrid {
  public static final String LOCK_PREFIX = "LOCK__";
  private final RedisTemplate redisTemplate;
  private final RedisMessageListenerContainer redisMessageListenerContainer;
  private final StringRedisTemplate stringRedisTemplate;
  private final List<RedisConnectionFactory> redisConnectionFactories;
  private final FencingTokenService fencingTokenService;
  private final RedisLockManager lockManager;

  @PostConstruct
  void init() {
    log.debug("RedisDataGrid initialized");
  }

  @Override
  public <K, V> KeyValueCache<K, V> getKeyValueCache(String name) {
    return new RedisKeyValueCache<>(redisTemplate.boundHashOps(name));
  }

  @Override
  public <V> ValueSet<V> getValueSet(String name) {
    return new RedisValueSet<>(redisTemplate.boundSetOps(name));
  }

  @Override
  public AtomicCounter getAtomicCounter(String name) {
    return new RedisAtomicCounter(stringRedisTemplate.boundValueOps(name));
  }

  @Override
  public <T> PubSubTopic<T> getPubSubTopic(String name) {
    return new RedisPubSubTopic<>(redisTemplate, redisMessageListenerContainer, name);
  }

  @Override
  public ClusterLock getClusterLock(String name) {
    RedisClusterLock rawLock =
        new RedisClusterLock(redisConnectionFactories, LOCK_PREFIX + name, fencingTokenService);

    LockManager<RedisClusterLock> lockManager = new RedisLockManager();
    return new ManagedClusterLock<>(rawLock, lockManager);
  }
}

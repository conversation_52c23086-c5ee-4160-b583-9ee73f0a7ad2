package com.solum.xplain.shared.datagrid.impl.redis

import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Timeout
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit

/**
 * Unit tests for RedisLockManager class.
 * These tests focus on the lock manager's behavior with mocked dependencies.
 */
class RedisLockManagerTest extends Specification {

  @Subject
  RedisLockManager lockManager

  RedisClusterLock mockLock

  def setup() {
    lockManager = new RedisLockManager()
    mockLock = Mock(RedisClusterLock)
  }

  def cleanup() {
    if (lockManager) {
      lockManager.shutdown()
    }
  }

  def "should initialize with proper defaults"() {
    expect:
    lockManager.heartbeatScheduler != null
    lockManager.heartbeatTasksByLockId != null
    lockManager.heartbeatTasksByLockId.isEmpty()
    !lockManager.heartbeatScheduler.isShutdown()
    RedisLockManager.RENEWAL_INTERVAL_MS == 7000
  }

  def "should successfully acquire lock with heartbeat"() {
    given:
    def lockKey = "test-lock"

    when:
    def result = lockManager.acquireWithHeartbeat(mockLock)

    then:
    1 * mockLock.tryLock() >> true
    2 * mockLock.getName() >> lockKey // Called for logging and startHeartbeat

    and:
    result == true
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.size() == 1
  }

  def "should fail to acquire lock when tryLock returns false"() {
    when:
    def result = lockManager.acquireWithHeartbeat(mockLock)

    then:
    1 * mockLock.tryLock() >> false
    0 * mockLock.getName()

    and:
    result == false
    lockManager.heartbeatTasksByLockId.isEmpty()
  }

  def "should successfully acquire lock with timeout and heartbeat"() {
    given:
    def lockKey = "test-lock-timeout"
    def timeout = 5L
    def unit = TimeUnit.SECONDS

    when:
    def result = lockManager.acquireWithHeartbeat(mockLock, timeout, unit)

    then:
    1 * mockLock.tryLock(timeout, unit) >> true
    2 * mockLock.getName() >> lockKey // Called for logging and startHeartbeat

    and:
    result == true
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.size() == 1
  }

  def "should fail to acquire lock with timeout when tryLock returns false"() {
    given:
    def timeout = 5L
    def unit = TimeUnit.SECONDS

    when:
    def result = lockManager.acquireWithHeartbeat(mockLock, timeout, unit)

    then:
    1 * mockLock.tryLock(timeout, unit) >> false
    0 * mockLock.getName()

    and:
    result == false
    lockManager.heartbeatTasksByLockId.isEmpty()
  }

  def "should successfully release lock and stop heartbeat"() {
    given:
    def lockKey = "test-lock-release"

    when:
    // First acquire the lock
    lockManager.acquireWithHeartbeat(mockLock)
    // Then release it
    lockManager.release(mockLock)

    then:
    1 * mockLock.tryLock() >> true
    3 * mockLock.getName() >> lockKey // Called for logging, startHeartbeat, and release
    1 * mockLock.unlock()

    and:
    !lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.isEmpty()
  }

  def "should handle release when no heartbeat exists"() {
    given:
    def lockKey = "non-existent-lock"

    when:
    lockManager.release(mockLock)

    then:
    1 * mockLock.getName() >> lockKey
    1 * mockLock.unlock()

    and:
    !lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.isEmpty()
  }

  def "should handle exception during unlock in release"() {
    given:
    def lockKey = "test-lock-exception"

    when:
    // First acquire the lock
    lockManager.acquireWithHeartbeat(mockLock)
    // Then release it (with exception)
    lockManager.release(mockLock)

    then:
    1 * mockLock.tryLock() >> true
    3 * mockLock.getName() >> lockKey
    1 * mockLock.unlock() >> { throw new RuntimeException("Unlock failed") }

    and:
    !lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.isEmpty()
    noExceptionThrown()
  }

  def "should start heartbeat for lock"() {
    given:
    def lockKey = "test-heartbeat"

    when:
    lockManager.startHeartbeat(mockLock)

    then:
    1 * mockLock.getName() >> lockKey

    and:
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.size() == 1
  }

  def "should replace existing heartbeat when starting new one for same lock"() {
    given:
    def lockKey = "test-heartbeat-replace"

    when:
    lockManager.startHeartbeat(mockLock)
    def firstTaskCount = lockManager.heartbeatTasksByLockId.size()
    lockManager.startHeartbeat(mockLock)
    def secondTaskCount = lockManager.heartbeatTasksByLockId.size()

    then:
    2 * mockLock.getName() >> lockKey

    and:
    firstTaskCount == 1
    secondTaskCount == 1
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)
  }

  @Timeout(5)
  def "should properly shutdown and cleanup all resources"() {
    given:
    def lockKey1 = "shutdown-lock-1"
    def lockKey2 = "shutdown-lock-2"
    def mockLock1 = Mock(RedisClusterLock)
    def mockLock2 = Mock(RedisClusterLock)

    when:
    // Acquire multiple locks
    lockManager.acquireWithHeartbeat(mockLock1)
    lockManager.acquireWithHeartbeat(mockLock2)
    lockManager.shutdown()

    then:
    1 * mockLock1.tryLock() >> true
    2 * mockLock1.getName() >> lockKey1
    1 * mockLock1.unlock()

    1 * mockLock2.tryLock() >> true
    2 * mockLock2.getName() >> lockKey2
    1 * mockLock2.unlock()

    and:
    lockManager.heartbeatTasksByLockId.isEmpty()
    lockManager.heartbeatScheduler.isShutdown()
  }

  def "should handle exceptions during shutdown gracefully"() {
    given:
    def lockKey = "shutdown-exception-lock"

    when:
    // Acquire lock
    lockManager.acquireWithHeartbeat(mockLock)
    lockManager.shutdown()

    then:
    1 * mockLock.tryLock() >> true
    2 * mockLock.getName() >> lockKey
    1 * mockLock.unlock() >> { throw new RuntimeException("Shutdown unlock failed") }

    and:
    lockManager.heartbeatTasksByLockId.isEmpty()
    lockManager.heartbeatScheduler.isShutdown()
    noExceptionThrown()
  }

  def "should handle concurrent acquire operations"() {
    given:
    def lockKey1 = "concurrent-lock-1"
    def lockKey2 = "concurrent-lock-2"
    def mockLock1 = Mock(RedisClusterLock)
    def mockLock2 = Mock(RedisClusterLock)

    when:
    def result1 = lockManager.acquireWithHeartbeat(mockLock1)
    def result2 = lockManager.acquireWithHeartbeat(mockLock2)

    then:
    1 * mockLock1.tryLock() >> true
    2 * mockLock1.getName() >> lockKey1

    1 * mockLock2.tryLock() >> true
    2 * mockLock2.getName() >> lockKey2

    and:
    result1 == true
    result2 == true
    lockManager.heartbeatTasksByLockId.size() == 2
    lockManager.heartbeatTasksByLockId.containsKey(lockKey1)
    lockManager.heartbeatTasksByLockId.containsKey(lockKey2)
  }

  def "should handle concurrent release operations"() {
    given:
    def lockKey1 = "concurrent-release-1"
    def lockKey2 = "concurrent-release-2"
    def mockLock1 = Mock(RedisClusterLock)
    def mockLock2 = Mock(RedisClusterLock)

    when:
    // Acquire both locks
    lockManager.acquireWithHeartbeat(mockLock1)
    lockManager.acquireWithHeartbeat(mockLock2)
    // Release both locks
    lockManager.release(mockLock1)
    lockManager.release(mockLock2)

    then:
    1 * mockLock1.tryLock() >> true
    3 * mockLock1.getName() >> lockKey1
    1 * mockLock1.unlock()

    1 * mockLock2.tryLock() >> true
    3 * mockLock2.getName() >> lockKey2
    1 * mockLock2.unlock()

    and:
    lockManager.heartbeatTasksByLockId.isEmpty()
  }

  def "should track heartbeat tasks correctly"() {
    given:
    def lockKeys = ["tracking-lock-1", "tracking-lock-2", "tracking-lock-3"]
    def mockLocks = lockKeys.collect { Mock(RedisClusterLock) }

    when:
    mockLocks.eachWithIndex { mock, index ->
      lockManager.acquireWithHeartbeat(mock)
    }

    then:
    mockLocks.eachWithIndex { mock, index ->
      1 * mock.tryLock() >> true
      2 * mock.getName() >> lockKeys[index]
    }

    and:
    lockManager.heartbeatTasksByLockId.size() == 3
    lockKeys.each { lockKey ->
      assert lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    }

    when:
    lockManager.release(mockLocks[1])

    then:
    1 * mockLocks[1].getName() >> lockKeys[1]
    1 * mockLocks[1].unlock()

    and:
    lockManager.heartbeatTasksByLockId.size() == 2
    !lockManager.heartbeatTasksByLockId.containsKey(lockKeys[1])
    lockManager.heartbeatTasksByLockId.containsKey(lockKeys[0])
    lockManager.heartbeatTasksByLockId.containsKey(lockKeys[2])
  }

  def "should verify renewal interval constant"() {
    expect:
    RedisLockManager.RENEWAL_INTERVAL_MS == 7000L
  }

  def "should create daemon threads for heartbeat scheduler"() {
    expect:
    lockManager.heartbeatScheduler != null
    !lockManager.heartbeatScheduler.isShutdown()
    !lockManager.heartbeatScheduler.isTerminated()
  }

  def "should handle rapid acquire and release cycles"() {
    given:
    def lockKey = "rapid-cycle-lock"

    when:
    (1..5).each {
      lockManager.acquireWithHeartbeat(mockLock)
      lockManager.release(mockLock)
    }

    then:
    5 * mockLock.tryLock() >> true
    15 * mockLock.getName() >> lockKey // 2 calls per acquire + 1 per release
    5 * mockLock.unlock()

    and:
    lockManager.heartbeatTasksByLockId.isEmpty()
    noExceptionThrown()
  }

  @Timeout(5)
  def "should properly shutdown and cleanup all resources"() {
    given:
    def lockKey1 = "shutdown-lock-1"
    def lockKey2 = "shutdown-lock-2"
    def mockLock1 = Mock(RedisClusterLock)
    def mockLock2 = Mock(RedisClusterLock)

    when:
    // Acquire multiple locks
    lockManager.acquireWithHeartbeat(mockLock1)
    lockManager.acquireWithHeartbeat(mockLock2)
    lockManager.shutdown()

    then:
    1 * mockLock1.tryLock() >> true
    2 * mockLock1.getName() >> lockKey1
    1 * mockLock1.unlock()

    1 * mockLock2.tryLock() >> true
    2 * mockLock2.getName() >> lockKey2
    1 * mockLock2.unlock()

    and:
    lockManager.heartbeatTasksByLockId.isEmpty()
    lockManager.heartbeatScheduler.isShutdown()
  }

  def "should handle exceptions during shutdown gracefully"() {
    given:
    def lockKey = "shutdown-exception-lock"

    when:
    // Acquire lock
    lockManager.acquireWithHeartbeat(mockLock)
    lockManager.shutdown()

    then:
    1 * mockLock.tryLock() >> true
    2 * mockLock.getName() >> lockKey
    1 * mockLock.unlock() >> { throw new RuntimeException("Shutdown unlock failed") }

    and:
    lockManager.heartbeatTasksByLockId.isEmpty()
    lockManager.heartbeatScheduler.isShutdown()
    noExceptionThrown()
  }

  def "should handle concurrent acquire operations"() {
    given:
    def lockKey1 = "concurrent-lock-1"
    def lockKey2 = "concurrent-lock-2"
    def mockLock1 = Mock(RedisClusterLock)
    def mockLock2 = Mock(RedisClusterLock)

    when:
    def result1 = lockManager.acquireWithHeartbeat(mockLock1)
    def result2 = lockManager.acquireWithHeartbeat(mockLock2)

    then:
    1 * mockLock1.tryLock() >> true
    2 * mockLock1.getName() >> lockKey1

    1 * mockLock2.tryLock() >> true
    2 * mockLock2.getName() >> lockKey2

    and:
    result1 == true
    result2 == true
    lockManager.heartbeatTasksByLockId.size() == 2
    lockManager.heartbeatTasksByLockId.containsKey(lockKey1)
    lockManager.heartbeatTasksByLockId.containsKey(lockKey2)
  }

  def "should verify renewal interval constant"() {
    expect:
    RedisLockManager.RENEWAL_INTERVAL_MS == 7000L
  }

  def "should create daemon threads for heartbeat scheduler"() {
    expect:
    lockManager.heartbeatScheduler != null
    !lockManager.heartbeatScheduler.isShutdown()
    !lockManager.heartbeatScheduler.isTerminated()
  }

  def "should handle rapid acquire and release cycles"() {
    given:
    def lockKey = "rapid-cycle-lock"

    when:
    (1..5).each {
      lockManager.acquireWithHeartbeat(mockLock)
      lockManager.release(mockLock)
    }

    then:
    5 * mockLock.tryLock() >> true
    15 * mockLock.getName() >> lockKey // 2 calls per acquire + 1 per release
    5 * mockLock.unlock()

    and:
    lockManager.heartbeatTasksByLockId.isEmpty()
    noExceptionThrown()
  }
}

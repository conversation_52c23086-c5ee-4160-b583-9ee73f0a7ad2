package com.solum.xplain.shared.datagrid.impl.redis

import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Timeout
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit

/**
 * Unit tests for RedisLockManager class.
 * These tests focus on the lock manager's behavior with mocked dependencies.
 */
class RedisLockManagerTest extends Specification {

  @Subject
  RedisLockManager lockManager

  RedisClusterLock mockLock
  ScheduledFuture mockFuture

  def setup() {
    lockManager = new RedisLockManager()
    mockLock = Mock(RedisClusterLock)
    mockFuture = Mock(ScheduledFuture)
  }

//  def cleanup() {
//    if (lockManager) {
//      lockManager.shutdown()
//    }
//  }

  def "should initialize with proper defaults"() {
    expect:
    lockManager.heartbeatScheduler != null
    lockManager.heartbeatTasksByLockId != null
    lockManager.heartbeatTasksByLockId.isEmpty()
    !lockManager.heartbeatScheduler.isShutdown()
    RedisLockManager.RENEWAL_INTERVAL_MS == 7000
  }


  def "should fail to acquire lock when tryLock returns false"() {
    given:
    def lockKey = "test-lock"
    mockLock.getName() >> lockKey
    mockLock.tryLock() >> false

    when:
    def result = lockManager.acquireWithHeartbeat(mockLock)

    then:
    result == false
    !lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.isEmpty()

    and:
    1 * mockLock.tryLock()
    0 * mockLock.getName()
  }

  def "should successfully acquire lock with timeout and heartbeat"() {
    given:
    def lockKey = "test-lock-timeout"
    def timeout = 5L
    def unit = TimeUnit.SECONDS
    mockLock.getName() >> lockKey
    mockLock.tryLock(timeout, unit) >> true

    when:
    def result = lockManager.acquireWithHeartbeat(mockLock, timeout, unit)

    then:
    result == true
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.size() == 1

    and:
    1 * mockLock.tryLock(timeout, unit)
    1 * mockLock.getName()
  }

  def "should fail to acquire lock with timeout when tryLock returns false"() {
    given:
    def lockKey = "test-lock-timeout"
    def timeout = 5L
    def unit = TimeUnit.SECONDS
    mockLock.getName() >> lockKey
    mockLock.tryLock(timeout, unit) >> false

    when:
    def result = lockManager.acquireWithHeartbeat(mockLock, timeout, unit)

    then:
    result == false
    !lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.isEmpty()

    and:
    1 * mockLock.tryLock(timeout, unit)
    0 * mockLock.getName()
  }

  def "should successfully release lock and stop heartbeat"() {
    given:
    def lockKey = "test-lock-release"
    mockLock.getName() >> lockKey
    mockLock.tryLock() >> true

    // First acquire the lock
    lockManager.acquireWithHeartbeat(mockLock)

    when:
    lockManager.release(mockLock)

    then:
    !lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.isEmpty()

    and:
    1 * mockLock.unlock()
  }

  def "should handle release when no heartbeat exists"() {
    given:
    def lockKey = "non-existent-lock"
    mockLock.getName() >> lockKey

    when:
    lockManager.release(mockLock)

    then:
    !lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.isEmpty()

    and:
    1 * mockLock.unlock()
  }

  def "should handle exception during unlock in release"() {
    given:
    def lockKey = "test-lock-exception"
    mockLock.getName() >> lockKey
    mockLock.tryLock() >> true
    mockLock.unlock() >> { throw new RuntimeException("Unlock failed") }

    // First acquire the lock
    lockManager.acquireWithHeartbeat(mockLock)

    when:
    lockManager.release(mockLock)

    then:
    !lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.isEmpty()
    noExceptionThrown()

    and:
    1 * mockLock.unlock()
  }

  def "should start heartbeat for lock and renew lock"() {
    given:
    def lockKey = "test-heartbeat"
    def newmock = Mock(new RedisClusterLock(_,lockKey,_))
    newmock.lockKey = lockKey
    newmock.getName() >> lockKey
    newmock.isCurrentLockValid() >> true
    newmock.isLockExpired() >> false
    newmock.renewLock() >> true

// Force stub activation
    println("Forced getName call: " + newmock.getName())

    when:
    lockManager.startHeartbeat(newmock)

// Wait a little to let heartbeat init
    Thread.sleep(100)

    then:
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    lockManager.heartbeatTasksByLockId.size() == 1
    (1.._) * newmock.getName()

    def task = lockManager.heartbeatTasksByLockId[lockKey]
    task != null
    !task.hasHeartBeatStopped

    cleanup:
    task.stop()

  }




  def "should replace existing heartbeat when starting new one for same lock"() {
    given:
    def lockKey = "test-heartbeat-replace"
    mockLock.getName() >> lockKey

    when:
    lockManager.startHeartbeat(mockLock)
    def firstTaskCount = lockManager.heartbeatTasksByLockId.size()
    lockManager.startHeartbeat(mockLock)
    def secondTaskCount = lockManager.heartbeatTasksByLockId.size()

    then:
    firstTaskCount == 1
    secondTaskCount == 1
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)

    and:
    2 * mockLock.getName()
  }

  @Timeout(5)
  def "should properly shutdown and cleanup all resources"() {
    given:
    def lockKey1 = "shutdown-lock-1"
    def lockKey2 = "shutdown-lock-2"
    def mockLock1 = Mock(RedisClusterLock)
    def mockLock2 = Mock(RedisClusterLock)

    mockLock1.getName() >> lockKey1
    mockLock1.tryLock() >> true
    mockLock2.getName() >> lockKey2
    mockLock2.tryLock() >> true

    // Acquire multiple locks
    lockManager.acquireWithHeartbeat(mockLock1)
    lockManager.acquireWithHeartbeat(mockLock2)

    when:
    lockManager.shutdown()

    then:
    lockManager.heartbeatTasksByLockId.isEmpty()
    lockManager.heartbeatScheduler.isShutdown()

    and:
    1 * mockLock1.unlock()
    1 * mockLock2.unlock()
  }

  def "should handle exceptions during shutdown gracefully"() {
    given:
    def lockKey = "shutdown-exception-lock"
    mockLock.getName() >> lockKey
    mockLock.tryLock() >> true
    mockLock.unlock() >> { throw new RuntimeException("Shutdown unlock failed") }

    // Acquire lock
    lockManager.acquireWithHeartbeat(mockLock)

    when:
    lockManager.shutdown()

    then:
    lockManager.heartbeatTasksByLockId.isEmpty()
    lockManager.heartbeatScheduler.isShutdown()
    noExceptionThrown()

    and:
    1 * mockLock.unlock()
  }

  def "should handle multiple locks during shutdown"() {
    given:
    def locks = []
    def mockLocks = []
    (1..5).each { i ->
      def lockKey = "multi-shutdown-lock-${i}"
      def mockLockInstance = Mock(RedisClusterLock)
      mockLockInstance.getName() >> lockKey
      mockLockInstance.tryLock() >> true
      mockLocks.add(mockLockInstance)
      locks.add(lockKey)
      lockManager.acquireWithHeartbeat(mockLockInstance)
    }

    expect:
    lockManager.heartbeatTasksByLockId.size() == 5

    when:
    lockManager.shutdown()

    then:
    lockManager.heartbeatTasksByLockId.isEmpty()
    lockManager.heartbeatScheduler.isShutdown()

    and:
    mockLocks.each { mockLockInstance ->
      1 * mockLockInstance.unlock()
    }
  }

  // Tests for HeartbeatTask behavior through the public interface
  @Timeout(3)
  def "should stop heartbeat when lock becomes invalid"() {
    given:
    def lockKey = "invalid-lock"
    mockLock.getName() >> lockKey
    mockLock.tryLock() >> true
    mockLock.isCurrentLockValid() >> false
    mockLock.isLockExpired() >> false

    // Start heartbeat
    lockManager.acquireWithHeartbeat(mockLock)

    when:
    // Wait a bit for heartbeat to potentially run
    Thread.sleep(100)

    then:
    // The heartbeat task should still be tracked initially
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)

    and:
    1 * mockLock.isCurrentLockValid() >> false
    0 * mockLock.renewLock() // Should not attempt renewal when invalid
  }

  @Timeout(3)
  def "should stop heartbeat when lock is expired"() {
    given:
    def lockKey = "expired-lock"
    mockLock.getName() >> lockKey
    mockLock.tryLock() >> true
    mockLock.isCurrentLockValid() >> true
    mockLock.isLockExpired() >> true

    // Start heartbeat
    lockManager.acquireWithHeartbeat(mockLock)

    when:
    // Wait a bit for heartbeat to potentially run
    Thread.sleep(100)

    then:
    // The heartbeat task should still be tracked initially
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)

    and:
    1 * mockLock.isLockExpired() >> true
    0 * mockLock.renewLock() // Should not attempt renewal when expired
  }

  @Timeout(3)
  def "should attempt renewal when lock is valid and not expired"() {
    given:
    def lockKey = "valid-lock"
    mockLock.getName() >> lockKey
    mockLock.tryLock() >> true
    mockLock.isCurrentLockValid() >> true
    mockLock.isLockExpired() >> false
    mockLock.renewLock() >> true

    // Start heartbeat
    lockManager.acquireWithHeartbeat(mockLock)

    when:
    // Wait a bit for heartbeat to potentially run
    Thread.sleep(100)

    then:
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)

    and:
    1 * mockLock.isCurrentLockValid() >> true
    1 * mockLock.isLockExpired() >> false
    1 * mockLock.renewLock() >> true
  }

  @Timeout(3)
  def "should stop heartbeat when renewal fails"() {
    given:
    def lockKey = "renewal-fail-lock"
    mockLock.getName() >> lockKey
    mockLock.tryLock() >> true
    mockLock.isCurrentLockValid() >> true
    mockLock.isLockExpired() >> false
    mockLock.renewLock() >> false

    // Start heartbeat
    lockManager.acquireWithHeartbeat(mockLock)

    when:
    // Wait a bit for heartbeat to potentially run
    Thread.sleep(100)

    then:
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)

    and:
    1 * mockLock.isCurrentLockValid() >> true
    1 * mockLock.isLockExpired() >> false
    1 * mockLock.renewLock() >> false
  }

  @Timeout(3)
  def "should handle exceptions during renewal gracefully"() {
    given:
    def lockKey = "renewal-exception-lock"
    mockLock.getName() >> lockKey
    mockLock.tryLock() >> true
    mockLock.isCurrentLockValid() >> { throw new RuntimeException("Validation failed") }

    // Start heartbeat
    lockManager.acquireWithHeartbeat(mockLock)

    when:
    // Wait a bit for heartbeat to potentially run
    Thread.sleep(100)

    then:
    lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    noExceptionThrown()

    and:
    1 * mockLock.isCurrentLockValid() >> { throw new RuntimeException("Validation failed") }
  }

  def "should handle concurrent acquire operations"() {
    given:
    def lockKey1 = "concurrent-lock-1"
    def lockKey2 = "concurrent-lock-2"
    def mockLock1 = Mock(RedisClusterLock)
    def mockLock2 = Mock(RedisClusterLock)

    mockLock1.getName() >> lockKey1
    mockLock1.tryLock() >> true
    mockLock2.getName() >> lockKey2
    mockLock2.tryLock() >> true

    when:
    def result1 = lockManager.acquireWithHeartbeat(mockLock1)
    def result2 = lockManager.acquireWithHeartbeat(mockLock2)

    then:
    result1 == true
    result2 == true
    lockManager.heartbeatTasksByLockId.size() == 2
    lockManager.heartbeatTasksByLockId.containsKey(lockKey1)
    lockManager.heartbeatTasksByLockId.containsKey(lockKey2)
  }

  def "should handle concurrent release operations"() {
    given:
    def lockKey1 = "concurrent-release-1"
    def lockKey2 = "concurrent-release-2"
    def mockLock1 = Mock(RedisClusterLock)
    def mockLock2 = Mock(RedisClusterLock)

    mockLock1.getName() >> lockKey1
    mockLock1.tryLock() >> true
    mockLock2.getName() >> lockKey2
    mockLock2.tryLock() >> true

    // Acquire both locks
    lockManager.acquireWithHeartbeat(mockLock1)
    lockManager.acquireWithHeartbeat(mockLock2)

    when:
    lockManager.release(mockLock1)
    lockManager.release(mockLock2)

    then:
    lockManager.heartbeatTasksByLockId.isEmpty()

    and:
    1 * mockLock1.unlock()
    1 * mockLock2.unlock()
  }

  def "should track heartbeat tasks correctly"() {
    given:
    def lockKeys = (1..3).collect { "tracking-lock-${it}" }
    def mockLocks = lockKeys.collect { lockKey ->
      def mock = Mock(RedisClusterLock)
      mock.getName() >> lockKey
      mock.tryLock() >> true
      return mock
    }

    when:
    mockLocks.each { lockManager.acquireWithHeartbeat(it) }

    then:
    lockManager.heartbeatTasksByLockId.size() == 3
    lockKeys.each { lockKey ->
      assert lockManager.heartbeatTasksByLockId.containsKey(lockKey)
    }

    when:
    lockManager.release(mockLocks[1])

    then:
    lockManager.heartbeatTasksByLockId.size() == 2
    !lockManager.heartbeatTasksByLockId.containsKey(lockKeys[1])
    lockManager.heartbeatTasksByLockId.containsKey(lockKeys[0])
    lockManager.heartbeatTasksByLockId.containsKey(lockKeys[2])
  }

  def "should handle null lock gracefully in release"() {
    when:
    lockManager.release(null)

    then:
    thrown(NullPointerException)
  }

  def "should verify renewal interval constant"() {
    expect:
    RedisLockManager.RENEWAL_INTERVAL_MS == 7000L
  }

  def "should create daemon threads for heartbeat scheduler"() {
    expect:
    lockManager.heartbeatScheduler != null
    !lockManager.heartbeatScheduler.isShutdown()
    !lockManager.heartbeatScheduler.isTerminated()
  }

  def "should handle rapid acquire and release cycles"() {
    given:
    def lockKey = "rapid-cycle-lock"
    mockLock.getName() >> lockKey
    mockLock.tryLock() >> true

    when:
    (1..10).each {
      lockManager.acquireWithHeartbeat(mockLock)
      lockManager.release(mockLock)
    }

    then:
    lockManager.heartbeatTasksByLockId.isEmpty()
    noExceptionThrown()

    and:
    10 * mockLock.tryLock()
    10 * mockLock.unlock()
  }
}
